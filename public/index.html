<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Task Management</title>

  <!-- HTMX for declarative AJAX -->
  <script src="https://unpkg.com/htmx.org@1.9.9"></script>

  <!-- Shoelace Web Components -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@shoelace-style/shoelace@2.12.0/cdn/themes/light.css" />
  <script type="module" src="https://cdn.jsdelivr.net/npm/@shoelace-style/shoelace@2.12.0/cdn/shoelace.js"></script>

  <style>
    :root {
      --sl-color-primary-500: #2563eb;
      --sl-color-primary-600: #1d4ed8;
      --sl-color-success-500: #10b981;
      --sl-color-danger-500: #ef4444;
    }

    body {
      font-family: var(--sl-font-sans);
      max-width: 800px;
      margin: 0 auto;
      padding: 2rem;
      color: var(--sl-color-neutral-900);
    }

    .app-container {
      display: grid;
      gap: 2rem;
    }

    .task-list {
      display: grid;
      gap: 1rem;
    }

    .task-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      border-radius: var(--sl-border-radius-medium);
      background-color: var(--sl-color-neutral-50);
      transition: transform 0.2s ease-in-out;
    }

    .task-item:hover {
      transform: translateY(-2px);
    }

    .task-form {
      display: grid;
      gap: 1rem;
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 0.5rem;
    }

    .spinner-container {
      display: none;
    }

    [data-htmx-request] .spinner-container {
      display: inline-flex;
    }

    .task-complete {
      text-decoration: line-through;
      opacity: 0.7;
    }

    /* Style for refresh spinner when HTMX request is active */
    .refresh-spinner.htmx-indicator {
      display: inline-flex !important; /* Or inline-block, adjust as needed */
      align-items: center;
      vertical-align: middle;
    }
    .refresh-spinner { /* Default hidden state */
      display: none;
    }
  </style>
</head>

<body>
  <div class="app-container">
    <header>
      <sl-card>
        <div slot="header">
          <h1>Task Management</h1>
        </div>

        <p>A minimal task manager built with Shoelace Web Components and HTMX.</p>
      </sl-card>
    </header>

    <!-- New Task Form with HTMX -->
    <section>
      <sl-card>
        <div slot="header">
          <h2>Add New Task</h2>
        </div>

        <form class="task-form" hx-post="/api/tasks" hx-target="#task-list" hx-swap="beforeend" hx-trigger="submit"
          hx-indicator=".spinner-container">

          <sl-input name="title" required placeholder="What needs to be done?"></sl-input>

          <sl-textarea name="description" placeholder="Add details (optional)"></sl-textarea>

          <sl-select name="priority" placeholder="Select priority">
            <sl-option value="low">Low</sl-option>
            <sl-option value="medium">Medium</sl-option>
            <sl-option value="high">High</sl-option>
          </sl-select>

          <div class="form-actions">
            <div class="spinner-container">
              <sl-spinner></sl-spinner> Processing...
            </div>

            <sl-button type="reset" variant="neutral">Clear</sl-button>
            <sl-button type="submit" variant="primary">Add Task</sl-button>
          </div>
        </form>
      </sl-card>
    </section>

    <!-- Task List Container -->
    <section>
      <sl-card>
        <div slot="header">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <h2>Your Tasks</h2>
            <sl-button size="small" variant="neutral" hx-get="/api/tasks" hx-target="#task-list" hx-trigger="click"
              hx-indicator=".refresh-spinner">
              <sl-icon slot="prefix" name="arrow-clockwise"></sl-icon>
              Refresh
              <span class="refresh-spinner">
                <sl-spinner style="font-size: 1em; --stroke-width: 2px;"></sl-spinner>
              </span>
            </sl-button>
          </div>
        </div>

        <!-- Task Filtering -->
        <div style="margin-bottom: 1.5rem;">
          <sl-button-group label="Filter tasks">
            <sl-button hx-get="/api/tasks" hx-target="#task-list" hx-indicator=".refresh-spinner" variant="default">
              All
            </sl-button>
            <sl-button hx-get="/api/tasks?status=active" hx-target="#task-list" hx-indicator=".refresh-spinner"
              variant="default">
              Active
            </sl-button>
            <sl-button hx-get="/api/tasks?status=completed" hx-target="#task-list" hx-indicator=".refresh-spinner"
              variant="default">
              Completed
            </sl-button>
          </sl-button-group>
        </div>

        <!-- Tasks will be loaded here by HTMX. Initial example items removed. -->
        <div id="task-list" class="task-list" hx-get="/api/tasks" hx-trigger="load" hx-indicator=".refresh-spinner">
          <!-- Server will populate this or return an empty state message if no tasks -->
        </div>

        <!-- Empty state (This is now primarily handled by server response, but can be a fallback or example) -->
        <!-- 
          The server's getTasksHandler returns an <sl-alert> if no tasks are found.
          If the initial hx-get on #task-list fails or if there's a desire for a purely client-side
          empty state before the first load, this could be styled to appear, but it's largely redundant now.
          For now, keeping it commented or removed is fine if the server handles the empty state.
        -->
        <!-- 
        <sl-alert variant="neutral" open style="display: none;">
          <sl-icon slot="icon" name="info-circle"></sl-icon>
          No tasks found. Add your first task above.
        </sl-alert> 
        -->
      </sl-card>
    </section>
  </div>

  <!-- Task Edit Dialog Template is removed as it's unused. Edit forms are rendered server-side. -->

  <script>
    // Optional minimal JavaScript for enhancing the HTMX experience
    document.addEventListener('DOMContentLoaded', () => {
      // Add event listener for HTMX after request events
      document.body.addEventListener('htmx:afterRequest', (event) => {
        // Show toast notifications on successful actions
        if (event.detail.successful) {
          // Create a toast notification
          const toast = Object.assign(document.createElement('sl-toast'), {
            variant: 'primary',
            duration: 3000,
            closable: true
          });

          // Set toast content based on the request
          const requestPath = event.detail.pathInfo?.requestPath;
          const requestMethod = event.detail.requestConfig?.method;

          if (requestPath && requestMethod) {
            if (requestPath.includes('/api/tasks') && requestMethod === 'POST') {
              toast.innerText = 'Task added successfully';
            } else if (requestPath.includes('/toggle')) {
              toast.innerText = 'Task status updated';
            } else if (requestPath.includes('/api/tasks') && requestMethod === 'PUT') {
              toast.innerText = 'Task updated successfully';
            } else if (requestPath.includes('/api/tasks') && requestMethod === 'DELETE') {
              toast.variant = 'danger';
              toast.innerText = 'Task deleted';
            }
          }

          // Show the toast
          if (toast.innerText) {
            document.body.appendChild(toast);
            toast.show();
          }
        }
      });
    });
  </script>
</body>

</html>