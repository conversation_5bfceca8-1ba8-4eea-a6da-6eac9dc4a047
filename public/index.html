<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Task Management</title>

  <!-- HTMX for declarative AJAX -->
  <script src="https://unpkg.com/htmx.org@1.9.9"></script>

  <!-- Shoelace Web Components -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@shoelace-style/shoelace@2.12.0/cdn/themes/light.css">
  <script type="module" src="https://cdn.jsdelivr.net/npm/@shoelace-style/shoelace@2.12.0/cdn/shoelace.js"></script>

  <style>
    :root {
      --sl-color-primary-500: #2563eb;
      --sl-color-primary-600: #1d4ed8;
      --sl-color-success-500: #10b981;
      --sl-color-danger-500: #ef4444;
    }

    body {
      font-family: var(--sl-font-sans);
      max-width: 800px;
      margin: 0 auto;
      padding: 2rem;
      color: var(--sl-color-neutral-900);
    }

    .app-container {
      display: grid;
      gap: 2rem;
    }

    .task-list {
      display: grid;
      gap: 1rem;
    }

    .task-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      border-radius: var(--sl-border-radius-medium);
      background-color: var(--sl-color-neutral-50);
      transition: transform 0.2s ease-in-out;
    }

    .task-item:hover {
      transform: translateY(-2px);
    }

    .task-form {
      display: grid;
      gap: 1rem;
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 0.5rem;
    }

    .spinner-container {
      display: none;
    }

    [data-htmx-request] .spinner-container {
      display: inline-flex;
    }

    .task-complete {
      text-decoration: line-through;
      opacity: 0.7;
    }

    /* Style for refresh spinner when HTMX request is active */
    .refresh-spinner.htmx-indicator {
      display: inline-flex !important;
      /* Or inline-block, adjust as needed */
      align-items: center;
      vertical-align: middle;
    }

    .refresh-spinner {
      /* Default hidden state */
      display: none;
    }
  </style>
</head>

<body>
  <div class="app-container">
    <header>
      <sl-card>
        <div slot="header">
          <h1>Task Management</h1>
        </div>

        <p>A minimal task manager built with Shoelace Web Components and HTMX.</p>
      </sl-card>
    </header>

    <!-- New Task Form with HTMX -->
    <section>
      <sl-card>
        <div slot="header">
          <h2>Add New Task</h2>
        </div>

        <form class="task-form" hx-post="/api/tasks" hx-target="#task-list" hx-swap="beforeend" hx-trigger="submit"
          hx-indicator=".spinner-container">

          <sl-input name="title" required placeholder="What needs to be done?"></sl-input>

          <sl-textarea name="description" placeholder="Add details (optional)"></sl-textarea>

          <sl-select name="priority" placeholder="Select priority" value="medium">
            <sl-option value="low">Low</sl-option>
            <sl-option value="medium">Medium</sl-option>
            <sl-option value="high">High</sl-option>
          </sl-select>

          <div class="form-actions">
            <div class="spinner-container">
              <sl-spinner></sl-spinner> Processing...
            </div>

            <sl-button type="reset" variant="neutral">Clear</sl-button>
            <sl-button type="submit" variant="primary">Add Task</sl-button>
          </div>
        </form>
      </sl-card>
    </section>

    <!-- Task List Container -->
    <section>
      <sl-card>
        <div slot="header">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <h2>Your Tasks</h2>
            <sl-button size="small" variant="neutral" hx-get="/api/tasks" hx-target="#task-list" hx-trigger="click"
              hx-indicator=".refresh-spinner">
              <sl-icon slot="prefix" name="arrow-clockwise"></sl-icon>
              Refresh
              <span class="refresh-spinner">
                <sl-spinner style="font-size: 1em; --stroke-width: 2px;"></sl-spinner>
              </span>
            </sl-button>
          </div>
        </div>

        <!-- Task Filtering -->
        <div style="margin-bottom: 1.5rem;">
          <sl-button-group label="Filter tasks">
            <sl-button hx-get="/api/tasks" hx-target="#task-list" hx-indicator=".refresh-spinner" variant="default">
              All
            </sl-button>
            <sl-button hx-get="/api/tasks?status=active" hx-target="#task-list" hx-indicator=".refresh-spinner"
              variant="default">
              Active
            </sl-button>
            <sl-button hx-get="/api/tasks?status=completed" hx-target="#task-list" hx-indicator=".refresh-spinner"
              variant="default">
              Completed
            </sl-button>
          </sl-button-group>
        </div>

        <!-- Tasks will be loaded here by HTMX. Initial example items removed. -->
        <div id="task-list" class="task-list" hx-get="/api/tasks" hx-trigger="load" hx-indicator=".refresh-spinner">
          <!-- Server will populate this or return an empty state message if no tasks -->
        </div>

        <!-- Empty state (This is now primarily handled by server response, but can be a fallback or example) -->
        <!--
          The server's getTasksHandler returns an <sl-alert> if no tasks are found.
          If the initial hx-get on #task-list fails or if there's a desire for a purely client-side
          empty state before the first load, this could be styled to appear, but it's largely redundant now.
          For now, keeping it commented or removed is fine if the server handles the empty state.
        -->
        <!--
        <sl-alert variant="neutral" open style="display: none;">
          <sl-icon slot="icon" name="info-circle"></sl-icon>
          No tasks found. Add your first task above.
        </sl-alert>
        -->
      </sl-card>
    </section>
  </div>

  <!-- Task Edit Dialog Template is removed as it's unused. Edit forms are rendered server-side. -->

  <script>
    // Optional minimal JavaScript for enhancing the HTMX experience
    document.addEventListener('DOMContentLoaded', () => {

      // Custom form serialization for Shoelace components
      document.body.addEventListener('htmx:configRequest', function (evt) {
        if (evt.detail.elt.matches('form.task-form')) {
          const form = evt.detail.elt;
          const formData = new FormData();

          // Get values from Shoelace components
          const titleInput = form.querySelector('sl-input[name="title"]');
          const descriptionInput = form.querySelector('sl-textarea[name="description"]');
          const prioritySelect = form.querySelector('sl-select[name="priority"]');

          if (titleInput) formData.append('title', titleInput.value || '');
          if (descriptionInput) formData.append('description', descriptionInput.value || '');
          if (prioritySelect) formData.append('priority', prioritySelect.value || 'medium');

          // Override the parameters with our custom form data
          evt.detail.parameters = {};
          for (const [key, value] of formData.entries()) {
            evt.detail.parameters[key] = value;
          }
        }
      });

      // Reset form after successful submission
      document.body.addEventListener('htmx:afterRequest', function (evt) {
        if (evt.detail.elt.matches('form.task-form') && evt.detail.xhr.status === 200) {
          const form = evt.detail.elt;
          const titleInput = form.querySelector('sl-input[name="title"]');
          const descriptionInput = form.querySelector('sl-textarea[name="description"]');
          const prioritySelect = form.querySelector('sl-select[name="priority"]');

          if (titleInput) titleInput.value = '';
          if (descriptionInput) descriptionInput.value = '';
          if (prioritySelect) prioritySelect.value = 'medium';
        }
      });
      // Generic error handler for HTMX requests (optional, but good for debugging)
      document.body.addEventListener('htmx:responseError', function (evt) {
        console.error("HTMX Response Error:", evt.detail.error, "From:", evt.detail.xhr);
        // Optionally show a generic error toast for network/server errors not handled by specific HTML responses
        // Ensure this doesn't conflict with HTML error responses from the server.
        if (!evt.detail.xhr.responseText.includes('<sl-alert')) { // Avoid double-toasting if server sent an HTML error
          const errorToast = Object.assign(document.createElement('sl-toast'), {
            variant: 'danger',
            duration: 5000,
            closable: true,
            innerText: `Request failed: ${evt.detail.error || evt.detail.xhr.statusText || 'Unknown error'}`
          });
          document.body.appendChild(errorToast);
          errorToast.show();
        }
      });

      // Function to create and show a toast
      function showToast(message, variant = 'primary', duration = 3000) {
        const toast = Object.assign(document.createElement('sl-toast'), {
          variant: variant,
          duration: duration,
          closable: true,
          innerHTML: message // innerHTML to allow icons or other HTML in message if needed
        });
        document.body.appendChild(toast);
        // It's important to remove the toast from the DOM after it hides to prevent buildup
        toast.addEventListener('sl-after-hide', () => {
          toast.remove();
        });
        toast.show();
      }

      // Event listeners for server-sent HX-Trigger events
      document.body.addEventListener('show-toast-task-created', () => {
        showToast('Task added successfully!', 'success'); // Changed variant to success for create
      });

      document.body.addEventListener('show-toast-task-updated', () => {
        showToast('Task updated successfully.', 'primary');
      });

      document.body.addEventListener('show-toast-task-toggled', () => {
        showToast('Task status updated.', 'neutral'); // Changed variant to neutral
      });

      document.body.addEventListener('show-toast-task-deleted', () => {
        showToast('Task deleted.', 'danger');
      });

      // The old htmx:afterRequest listener is removed as per instructions
      // document.body.addEventListener('htmx:afterRequest', (event) => {
      //   // ... old logic was here ...
      // });
    });
  </script>
</body>

</html>